<?php

namespace DD\App\Admin;

use DD\App\Models\Tag;
use DD\App\Api\GetResponseApiClient;

class TagManager
{
    private static function getTableName(): string
    {
        global $wpdb;
        return $wpdb->prefix . 'cf7_getresponse_tags';
    }

    public static function createTag(string $tagName, string $listId, bool $consentRequired, string $consentText): ?Tag
    {
        global $wpdb;

        $result = $wpdb->insert(
            self::getTableName(),
            [
                'tag_name' => sanitize_text_field($tagName),
                'list_id' => sanitize_text_field($listId),
                'consent_required' => $consentRequired ? 1 : 0,
                'consent_text' => sanitize_textarea_field($consentText),
            ],
            ['%s', '%s', '%d', '%s']
        );

        if ($result === false) {
            return null;
        }

        return self::getTagById($wpdb->insert_id);
    }

    public static function updateTag(int $id, string $tagName, string $listId, bool $consentRequired, string $consentText): bool
    {
        global $wpdb;

        $result = $wpdb->update(
            self::getTableName(),
            [
                'tag_name' => sanitize_text_field($tagName),
                'list_id' => sanitize_text_field($listId),
                'consent_required' => $consentRequired ? 1 : 0,
                'consent_text' => sanitize_textarea_field($consentText),
            ],
            ['id' => $id],
            ['%s', '%s', '%d', '%s'],
            ['%d']
        );

        return $result !== false;
    }

    public static function deleteTag(int $id): bool
    {
        global $wpdb;

        $result = $wpdb->delete(
            self::getTableName(),
            ['id' => $id],
            ['%d']
        );

        return $result !== false;
    }

    public static function getTagById(int $id): ?Tag
    {
        global $wpdb;

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM " . self::getTableName() . " WHERE id = %d",
                $id
            ),
            ARRAY_A
        );

        if ($result === null) {
            return null;
        }

        return Tag::fromDatabase($result);
    }

    public static function getTagByName(string $tagName): ?Tag
    {
        global $wpdb;

        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM " . self::getTableName() . " WHERE tag_name = %s",
                $tagName
            ),
            ARRAY_A
        );

        if ($result === null) {
            return null;
        }

        return Tag::fromDatabase($result);
    }

    /** @return Tag[] */
    public static function getAllTags(): array
    {
        global $wpdb;

        $results = $wpdb->get_results(
            "SELECT * FROM " . self::getTableName() . " ORDER BY created_at DESC",
            ARRAY_A
        );

        $tags = [];
        foreach ($results as $result) {
            $tags[] = Tag::fromDatabase($result);
        }

        return $tags;
    }

    public static function tagNameExists(string $tagName, ?int $excludeId = null): bool
    {
        global $wpdb;

        $sql = "SELECT COUNT(*) FROM " . self::getTableName() . " WHERE tag_name = %s";
        $params = [$tagName];

        if ($excludeId !== null) {
            $sql .= " AND id != %d";
            $params[] = $excludeId;
        }

        $count = $wpdb->get_var($wpdb->prepare($sql, $params));

        return (int) $count > 0;
    }

    /** @return array{success: bool, lists?: array, error?: string} */
    public static function getGetResponseLists(): array
    {
        $apiKey = new ApiKey();
        
        if (!$apiKey->hasApiKey()) {
            return [
                'success' => false,
                'error' => 'Brak klucza API GetResponse'
            ];
        }

        $client = new GetResponseApiClient($apiKey->getApiKey());
        $lists = $client->getListsList();

        return [
            'success' => true,
            'lists' => array_map(fn($list) => $list->toArray(), $lists)
        ];
    }
}
