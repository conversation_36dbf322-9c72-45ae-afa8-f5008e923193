<?php

namespace DD\App\Admin;

class ApiKey
{
    private const OPTION_NAME = 'dd_gr_cf7_key';

    public function getApiKey(): string
    {
        return get_option(self::OPTION_NAME, '');
    }

    public function saveApiKey(string $apiKey): bool
    {
        return update_option(self::OPTION_NAME, sanitize_text_field($apiKey));
    }

    public function deleteApiKey(): bool
    {
        return delete_option(self::OPTION_NAME);
    }

    public function hasApiKey(): bool
    {
        return !empty($this->getApiKey());
    }
}
