<?php

namespace DD\App\Models;

class Tag
{
    public int $id;
    public string $tagName;
    public string $listId;
    public bool $consentRequired;
    public string $consentText;
    public string $createdAt;
    public string $updatedAt;

    public function __construct(
        int $id,
        string $tagName,
        string $listId,
        bool $consentRequired,
        string $consentText,
        string $createdAt,
        string $updatedAt
    ) {
        $this->id = $id;
        $this->tagName = $tagName;
        $this->listId = $listId;
        $this->consentRequired = $consentRequired;
        $this->consentText = $consentText;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
    }

    /** @param array{id: int, tag_name: string, list_id: string, consent_required: int, consent_text: string, created_at: string, updated_at: string} $data */
    public static function fromDatabase(array $data): self
    {
        return new self(
            (int) $data['id'],
            $data['tag_name'],
            $data['list_id'],
            (bool) $data['consent_required'],
            $data['consent_text'],
            $data['created_at'],
            $data['updated_at']
        );
    }

    /** @return array{id: int, tag_name: string, list_id: string, consent_required: int, consent_text: string, created_at: string, updated_at: string} */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'tag_name' => $this->tagName,
            'list_id' => $this->listId,
            'consent_required' => $this->consentRequired ? 1 : 0,
            'consent_text' => $this->consentText,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }
}
