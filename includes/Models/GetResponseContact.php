<?php

namespace DD\App\Models;

class GetResponseContact
{
    public string $email;
    public ?string $name;

    public function __construct(string $email, ?string $name = null)
    {
        $this->email = $email;
        $this->name = $name;
    }

    /** @return array{email: string, name?: string} */
    public function toApiArray(): array
    {
        $data = ['email' => $this->email];
        
        if ($this->name !== null && $this->name !== '') {
            $data['name'] = $this->name;
        }
        
        return $data;
    }
}
