<?php

namespace DD\App\Models;

class GetResponseList
{
    public string $id;
    public string $name;
    public string $campaignId;

    public function __construct(string $id, string $name, string $campaignId)
    {
        $this->id = $id;
        $this->name = $name;
        $this->campaignId = $campaignId;
    }

    /** @return array{id: string, name: string, campaignId: string} */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'campaignId' => $this->campaignId,
        ];
    }

    /** @param array{campaignId: string, name: string} $data */
    public static function fromApiResponse(array $data): self
    {
        return new self(
            $data['campaignId'],
            $data['name'],
            $data['campaignId']
        );
    }
}
