<?php

namespace DD\App\CF7;

use DD\App\Admin\TagManager;

class TagHandler
{
    public function __construct()
    {
        add_action('wpcf7_init', [$this, 'addFormTag']);
        add_filter('wpcf7_validate_dd-getresponse', [$this, 'validateTag'], 10, 2);
    }

    public function addFormTag(): void
    {
        wpcf7_add_form_tag('dd-getresponse', [$this, 'formTagHandler'], ['name-attr' => true]);
    }

    /** @param \WPCF7_FormTag $tag */
    public function formTagHandler($tag): string
    {
        if (empty($tag->name)) {
            return '';
        }

        $tagName = $tag->get_option('tag', '', true);
        if (empty($tagName)) {
            return '<p style="color: red;">Błąd: Brak parametru "tag" w tagu dd-getresponse</p>';
        }

        $dbTag = TagManager::getTagByName($tagName);
        if (!$dbTag) {
            return '<p style="color: red;">Błąd: Nie znaleziono tagu "' . esc_html($tagName) . '"</p>';
        }

        $atts = [];
        $atts['type'] = 'checkbox';
        $atts['name'] = $tag->name;
        $atts['id'] = $tag->get_id_option();
        $atts['class'] = $tag->get_class_option();
        $atts['value'] = '1';

        if ($dbTag->consentRequired) {
            $atts['aria-required'] = 'true';
            $atts['required'] = 'required';
        }

        $atts = wpcf7_format_atts($atts);

        $html = sprintf(
            '<span class="wpcf7-form-control-wrap" data-name="%1$s">',
            esc_attr($tag->name)
        );

        $html .= sprintf(
            '<label><input %1$s /> %2$s</label>',
            $atts,
            esc_html($dbTag->consentText)
        );

        $html .= '</span>';

        return $html;
    }

    /** @param \WPCF7_Validation $result */
    public function validateTag($result, $tag)
    {
        $tagName = $tag->get_option('tag', '', true);
        if (empty($tagName)) {
            return $result;
        }

        $dbTag = TagManager::getTagByName($tagName);
        if (!$dbTag || !$dbTag->consentRequired) {
            return $result;
        }

        $submission = \WPCF7_Submission::get_instance();
        if (!$submission) {
            return $result;
        }

        $posted_data = $submission->get_posted_data();

        if (empty($posted_data[$tag->name])) {
            $result->invalidate($tag, 'To pole jest wymagane.');
        }

        return $result;
    }
}
