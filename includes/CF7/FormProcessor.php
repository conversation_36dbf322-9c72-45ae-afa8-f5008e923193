<?php

namespace DD\App\CF7;

use DD\App\Admin\TagManager;
use DD\App\Admin\ApiKey;
use DD\App\Api\GetResponseApiClient;
use DD\App\Models\GetResponseContact;

class FormProcessor
{
    public function __construct()
    {
        add_action('wpcf7_before_send_mail', [$this, 'processForm']);
    }

    /** @param \WPCF7_ContactForm $contact_form */
    public function processForm($contact_form): void
    {
        $submission = \WPCF7_Submission::get_instance();
        if (!$submission) {
            return;
        }

        $posted_data = $submission->get_posted_data();
        $form_tags = $contact_form->scan_form_tags(['type' => 'dd-getresponse']);

        foreach ($form_tags as $form_tag) {
            $field_name = $form_tag->name;
            
            if (empty($posted_data[$field_name])) {
                continue;
            }

            $tagName = $form_tag->get_option('tag', '', true);
            if (empty($tagName)) {
                continue;
            }

            $dbTag = TagManager::getTagByName($tagName);
            if (!$dbTag) {
                continue;
            }

            $email = $this->extractEmail($posted_data);
            if (empty($email)) {
                continue;
            }

            $name = $this->extractName($posted_data);
            $contact = new GetResponseContact($email, $name);

            if (!$this->addContactToGetResponse($contact, $dbTag->listId)) {
                $submission->set_status('mail_failed');
                $submission->set_response('Zapis na newsletter nie powiódł się. Spróbuj ponownie później.');
                return;
            }
        }
    }

    /** @param array<string, mixed> $posted_data */
    private function extractEmail(array $posted_data): string
    {
        $email_fields = ['email', 'your-email', 'e-mail', 'mail'];
        
        foreach ($email_fields as $field) {
            if (!empty($posted_data[$field]) && is_email($posted_data[$field])) {
                return sanitize_email($posted_data[$field]);
            }
        }

        return '';
    }

    /** @param array<string, mixed> $posted_data */
    private function extractName(array $posted_data): ?string
    {
        $name_fields = ['name', 'your-name', 'first-name', 'firstname', 'imie'];
        
        foreach ($name_fields as $field) {
            if (!empty($posted_data[$field])) {
                return sanitize_text_field($posted_data[$field]);
            }
        }

        return null;
    }

    private function addContactToGetResponse(GetResponseContact $contact, string $listId): bool
    {
        $apiKey = new ApiKey();
        
        if (!$apiKey->hasApiKey()) {
            error_log('GetResponse CF7: Brak klucza API');
            return false;
        }

        $client = new GetResponseApiClient($apiKey->getApiKey());
        return $client->addContactToList($contact, $listId);
    }
}
