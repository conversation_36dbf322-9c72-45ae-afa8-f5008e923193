<?php

namespace DD\App\Database;

class Migration
{
    public static function createTables(): void
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'cf7_getresponse_tags';
        
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            tag_name varchar(255) NOT NULL,
            list_id varchar(255) NOT NULL,
            consent_required tinyint(1) NOT NULL DEFAULT 0,
            consent_text text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY tag_name (tag_name)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbD<PERSON><PERSON>($sql);
    }

    public static function dropTables(): void
    {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7_getresponse_tags';
        $wpdb->query("DROP TABLE IF EXISTS $table_name");
    }
}
