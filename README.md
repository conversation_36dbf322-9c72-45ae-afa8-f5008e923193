# GetResponse CF7 Integration

Wtyczka WordPress do integracji Contact Form 7 z GetResponse API.

## Wymagania

- WordPress 5.0+
- PHP 7.4+
- Contact Form 7
- Konto GetResponse z aktywnym API

## Instalacja

1. Skopiuj pliki wtyczki do katalogu `/wp-content/plugins/getresponse-cf7-plugin/`
2. Uruchom `composer install` w katalogu wtyczki
3. Aktywuj wtyczkę w panelu administracyjnym WordPress

## Konfiguracja

### 1. Ustawienie klucza API

1. Przejdź do **Ustawienia > GetResponse CF7**
2. Wprowadź klucz API z panelu GetResponse
3. Kliknij **Zapisz**

### 2. Tworzenie tagów CF7

1. Przejdź do **Ustawienia > Zarządzaj tagami**
2. Wypełnij formularz:
   - **Nazwa tagu**: Unikatowa nazwa (np. "newsletter")
   - **Lista GetResponse**: Wybierz z listy (kliknij "Załaduj listy")
   - **Zgoda wymagana**: Czy checkbox musi być zaznaczony
   - **Treść zgody**: Tekst wyświetlany przy checkboxie
3. Kliknij **Dodaj tag**

### 3. Użycie w formularzach CF7

Dodaj tag do formularza Contact Form 7:

```
[dd-getresponse newsletter tag="newsletter"]
```

Gdzie:
- `newsletter` - nazwa pola w formularzu
- `tag="newsletter"` - nazwa tagu utworzonego w panelu

## Przykład kompletnego formularza

```
<label> Twoje imię
    [text* your-name] </label>

<label> Twój email
    [email* your-email] </label>

<label> Twoja wiadomość
    [textarea your-message] </label>

[dd-getresponse newsletter tag="newsletter"]

[submit "Wyślij"]
```

## Mapowanie pól

Wtyczka automatycznie mapuje pola:

- **Email**: `email`, `your-email`, `e-mail`, `mail`
- **Imię**: `name`, `your-name`, `first-name`, `firstname`, `imie`

## Funkcjonalności

- ✅ Zarządzanie kluczem API GetResponse
- ✅ Tworzenie i edycja tagów CF7
- ✅ Automatyczne pobieranie list z GetResponse
- ✅ Walidacja wymagalności zgody
- ✅ Dodawanie kontaktów do list GetResponse
- ✅ Obsługa błędów API
- ✅ Logowanie błędów

## Struktura plików

```
includes/
├── Admin/
│   ├── AdminMenu.php      # Panel administracyjny
│   ├── ApiKey.php         # Zarządzanie kluczem API
│   └── TagManager.php     # Zarządzanie tagami
├── Api/
│   └── GetResponseApiClient.php  # Klient API GetResponse
├── CF7/
│   ├── FormProcessor.php  # Przetwarzanie formularzy
│   └── TagHandler.php     # Obsługa tagów CF7
├── Database/
│   └── Migration.php      # Migracje bazy danych
├── Models/
│   ├── GetResponseContact.php
│   ├── GetResponseList.php
│   └── Tag.php
└── Plugin.php             # Główna klasa wtyczki

assets/
├── css/
│   └── admin.css          # Style panelu admin
└── js/
    └── admin.js           # JavaScript panelu admin
```

## Baza danych

Wtyczka tworzy tabelę `wp_cf7_getresponse_tags` z polami:
- `id` - ID tagu
- `tag_name` - Nazwa tagu (unikatowa)
- `list_id` - ID listy w GetResponse
- `consent_required` - Czy zgoda jest wymagana
- `consent_text` - Treść zgody
- `created_at` - Data utworzenia
- `updated_at` - Data aktualizacji

## Troubleshooting

### Błąd "Contact Form 7 nie jest aktywny"
Zainstaluj i aktywuj wtyczkę Contact Form 7.

### Błąd "Brak klucza API GetResponse"
Wprowadź poprawny klucz API w ustawieniach wtyczki.

### Błąd "Zapis na newsletter nie powiódł się"
Sprawdź logi błędów WordPress - wtyczka loguje szczegóły błędów API.

### Listy nie ładują się
Sprawdź czy klucz API jest poprawny i czy ma odpowiednie uprawnienia.

## Wsparcie

W przypadku problemów sprawdź logi błędów WordPress lub skontaktuj się z deweloperem.
