# Instrukcje instalacji - GetResponse CF7 Integration

## Wymagania systemowe

- WordPress 5.0+
- PHP 7.4+ z rozszerzeniami:
  - curl
  - json
  - mbstring
- Contact Form 7 (akty<PERSON><PERSON> w<PERSON>)
- Composer

## Instalacja dla deweloperów

### 1. Klonowanie/kopiowanie plików

```bash
# Skopiuj pliki do katalogu wtyczek WordPress
cp -r . /path/to/wordpress/wp-content/plugins/getresponse-cf7-plugin/
cd /path/to/wordpress/wp-content/plugins/getresponse-cf7-plugin/
```

### 2. Instal<PERSON><PERSON>

```bash
composer install --no-dev --optimize-autoloader
```

### 3. Aktywacja wtyczki

1. Zaloguj się do panelu administracyjnego WordPress
2. Przejdź do **Wtyczki > Zainstalowane wtyczki**
3. <PERSON><PERSON><PERSON><PERSON><PERSON> "GetResponse CF7 Integration - Devanti"
4. <PERSON><PERSON><PERSON><PERSON> **Aktywuj**

### 4. Weryfikacja instalacji

Po aktywacji sprawdź czy:
- W menu **Ustawienia** pojawiła się opcja **GetResponse CF7**
- W menu **Ustawienia** pojawiła się opcja **Zarządzaj tagami**
- W logach nie ma błędów związanych z wtyczką

## Konfiguracja początkowa

### 1. Uzyskanie klucza API GetResponse

1. Zaloguj się do panelu GetResponse
2. Przejdź do **Integracje i API > API**
3. Wygeneruj nowy klucz API
4. Skopiuj klucz

### 2. Konfiguracja wtyczki

1. W WordPress przejdź do **Ustawienia > GetResponse CF7**
2. Wklej klucz API
3. Kliknij **Zapisz**

### 3. Test połączenia

1. Przejdź do **Ustawienia > Zarządzaj tagami**
2. Kliknij **Załaduj listy**
3. Sprawdź czy listy z GetResponse zostały załadowane

## Struktura bazy danych

Po aktywacji wtyczka automatycznie utworzy tabelę:

```sql
CREATE TABLE wp_cf7_getresponse_tags (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    tag_name varchar(255) NOT NULL,
    list_id varchar(255) NOT NULL,
    consent_required tinyint(1) NOT NULL DEFAULT 0,
    consent_text text NOT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY tag_name (tag_name)
);
```

## Opcje WordPress

Wtyczka używa następujących opcji w tabeli `wp_options`:

- `dd_gr_cf7_key` - Klucz API GetResponse

## Testowanie

### Test podstawowej funkcjonalności

1. Utwórz nowy tag w panelu administracyjnym
2. Dodaj tag do formularza CF7
3. Wypełnij i wyślij formularz
4. Sprawdź czy kontakt pojawił się w GetResponse

### Test walidacji

1. Utwórz tag z wymaganą zgodą
2. Spróbuj wysłać formularz bez zaznaczenia checkboxa
3. Sprawdź czy pojawił się błąd walidacji

## Rozwiązywanie problemów

### Błędy podczas aktywacji

```bash
# Sprawdź uprawnienia plików
chmod -R 755 /path/to/plugin/
chown -R www-data:www-data /path/to/plugin/

# Sprawdź czy Composer zainstalował zależności
ls -la vendor/autoload.php
```

### Błędy API

```bash
# Włącz debug w WordPress (wp-config.php)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

# Sprawdź logi
tail -f /path/to/wordpress/wp-content/debug.log
```

### Problemy z bazą danych

```sql
-- Sprawdź czy tabela została utworzona
SHOW TABLES LIKE '%cf7_getresponse_tags%';

-- Sprawdź strukturę tabeli
DESCRIBE wp_cf7_getresponse_tags;

-- Sprawdź opcje wtyczki
SELECT * FROM wp_options WHERE option_name LIKE '%dd_gr_cf7%';
```

## Deinstalacja

### Ręczna deinstalacja

```sql
-- Usuń tabelę
DROP TABLE IF EXISTS wp_cf7_getresponse_tags;

-- Usuń opcje
DELETE FROM wp_options WHERE option_name LIKE '%dd_gr_cf7%';
```

```bash
# Usuń pliki wtyczki
rm -rf /path/to/wordpress/wp-content/plugins/getresponse-cf7-plugin/
```

## Środowisko deweloperskie

### PHPStan

```bash
# Analiza statyczna kodu
vendor/bin/phpstan analyse includes/ --level=8
```

### Debugging

Wtyczka loguje błędy do standardowego logu WordPress. Włącz debugging:

```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

